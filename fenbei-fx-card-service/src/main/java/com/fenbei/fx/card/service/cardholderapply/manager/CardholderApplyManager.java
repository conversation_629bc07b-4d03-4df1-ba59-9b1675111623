package com.fenbei.fx.card.service.cardholderapply.manager;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.common.threadpool.AirThreadPoolExecutor;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.dao.cardholderapply.CardholderApplyDAO;
import com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO;
import com.fenbei.fx.card.service.cardholder.dto.*;
import com.fenbei.fx.card.service.cardholder.manager.CardholderManager;
import com.fenbei.fx.card.service.cardholderapply.converter.CardholderApplyConverter;
import com.fenbei.fx.card.service.cardholderapply.dto.*;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.AirCreateCardHolderRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.AirCreateCardholderRpcRspDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.AirUpdateCardHolderRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.CardHolderRpcRespDTO;
import com.fenbeitong.dech.api.service.airwallex.IAirWallexCardHolderService;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.page.Page;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.fenbei.fx.card.common.constant.CoreConstant.NATION_CODE_CHN;
import static com.fenbei.fx.card.common.constant.GlobalCoreResponseCode.*;
import static com.fenbei.fx.card.constants.RedisKeyConstant.APPLY_MODIFY_REDIS_KEY_PRE;
import static com.fenbei.fx.card.constants.RedisKeyConstant.APPLY_REDIS_KEY_PRE;

/**
 * 持卡人操作申请 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardholderApplyManager extends BaseManagerImpl<CardholderApplyDAO, CardholderApplyPO, CardholderApplyDTO, CardholderApplyConverter> {

    @Autowired
    private RedissonService redissonService;

    @DubboReference
    private IAirWallexCardHolderService iAirWallexCardHolderService;

    @DubboReference
    protected IBaseEmployeeExtService iBaseEmployeeExtService;


    public static CardholderApplyManager me() {
        return SpringUtil.getBean(CardholderApplyManager.class);
    }

    public List<CardholderApplyListResDTO> queryByPhone(String phone) {
        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderApplyPO.DB_COL_PHONE, phone);
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, 0);
        queryWrapper.in(CardholderApplyPO.DB_COL_APPLY_STATUS, CardholderApplyStatusEnum.getNotRefusedCodes());
        List<CardholderApplyDTO> list = this.findList(queryWrapper);
        return this.converter.convertToCardholderApplyListResDTOList(list);
    }

    public List<CardholderApplyListResDTO> queryByEmail(String email) {
        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderApplyPO.DB_COL_EMAIL, email);
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, 0);
        queryWrapper.in(CardholderApplyPO.DB_COL_APPLY_STATUS, CardholderApplyStatusEnum.getNotRefusedCodes());
        List<CardholderApplyDTO> list = this.findList(queryWrapper);
        return this.converter.convertToCardholderApplyListResDTOList(list);
    }

    public CardholderApplyDTO applyCreate(CardholderApplyAddReqDTO reqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(user.getUser_id(), user.getCompany_id());
        log.info("CardholderApplyManager applyCreate reqDTO={},employeeContract={}", JsonUtils.toJson(reqDTO), JsonUtils.toJson(employeeContract));

        String lockKey = APPLY_REDIS_KEY_PRE + employeeContract.getEmployee_id();
        try {
            boolean tryLock = redissonService.tryLock(lockKey);
            if (!tryLock) {
                throw new FxCardException(OPERATION_OFTEN);
            }
            return doApplyCreate(reqDTO, employeeContract);
        } catch (InterruptedException e) {
            throw new FxCardException(OPERATION_OFTEN);
        } catch (FinhubException e) {
            log.warn("【持卡人申请修改并审批修改异常】：{}", e);
            throw e;
        } catch (Exception e) {
            log.error("【持卡人申请创建异常】：{}", e);
            throw new FxCardException(EXCEPTION);
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                log.error("持卡人申请释放锁失败：{}", lockKey, e);
            }
        }

    }

    private CardholderApplyDTO doApplyCreate(CardholderApplyAddReqDTO reqDTO, EmployeeContract employeeContract) {

        applyCheck(reqDTO, employeeContract);

        CardholderApplyPO applyPO = CopyUtils.convert(reqDTO, CardholderApplyPO.class);
        applyPO.setCompanyId(employeeContract.getCompany_id());
        applyPO.setEmployeeId(employeeContract.getEmployee_id());
        applyPO.setEmployeeName(employeeContract.getName());
        applyPO.setName(reqDTO.getName());
        applyPO.setCardPlatform(ObjectUtils.isEmpty(reqDTO.getCardPlatform())?CardPlatformEnum.AIRWALLEX.getCode() : reqDTO.getCardPlatform());
        identificationHandle(applyPO, reqDTO.getIdentificationExpiryType(), reqDTO.getIdentificationExpiryDate());
        // 操作申请id
        applyPO.setApplyId(BizIdUtils.getCardholderApplyId());
        // 持卡人id
        applyPO.setFxCardholderId(BizIdUtils.getCardholderId());
        // 申请类型 创建
        applyPO.setApplyType(1);
        // 申请状态 待审核
        applyPO.setApplyStatus(CardholderApplyStatusEnum.APPLYING.getKey());
        // 创建人
        applyPO.setCreateUserId(employeeContract.getEmployee_id());

        applyPO.setAddress(JsonUtils.toJson(reqDTO.getAddressDto()));

        applyPO.setPostalAddress(JsonUtils.toJson(reqDTO.getPostalAddressDto()));

        log.info("CardholderApplyManager doApplyCreate params={}", JsonUtils.toJson(applyPO));

        this.save(applyPO);

        CardholderApplyDTO cardholderApplyDTO = converter.poToDTO(applyPO);
        return cardholderApplyDTO;

    }

    private void applyCheck(CardholderApplyAddReqDTO reqDTO, EmployeeContract employeeContract) {
        //当前人是否存在持卡人信息
        checkCardholderExist(employeeContract);
        // 企业账号是否正常
        // checkCompanyActiveAcct(employeeContract.getCompany_id());
        // 手机号格式 使用企业绑定的
        //checkPhoneNumFormat(employeeContract.getPhone_num());
        // 邮箱格式
        emailCheck(reqDTO.getEmail(), reqDTO.getApplyId(), reqDTO.getFxCardholderId());

        if (StringUtils.isBlank(reqDTO.getNationCode())){
            reqDTO.setNationCode(NATION_CODE_CHN);
        }
        //地址信息检查
        AddressDto address = reqDTO.getAddressDto();
        addressCheck(address);
        AddressDto postalAddress = reqDTO.getPostalAddressDto();
        if (Objects.nonNull(postalAddress)){
            addressCheck(postalAddress);
        }
    }

    private void checkCardholderExist(EmployeeContract employeeContract) {
        CardholderOrApplyListReqDTO reqDto = new CardholderOrApplyListReqDTO();
        reqDto.setCompanyId(employeeContract.getCompany_id());
        reqDto.setEmployeeId(employeeContract.getEmployee_id());
        List<CardholderDTO> cardholders = CardholderManager.me().findCardholderByEmployeeId(reqDto);
        if (CollectionUtils.isNotEmpty(cardholders)){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDER_HAS_EXIST);
        }
        List<CardholderApplyDTO> cardholderApplys = this.findCardholderApplyByEmployeeId(reqDto);
        if (CollectionUtils.isNotEmpty(cardholderApplys)){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_HAS_EXIST);
        }
    }

    private void addressCheck(AddressDto address) {
        if (Objects.isNull(address)||Objects.isNull(address.getCountry())
            ||Objects.isNull(address.getState())||Objects.isNull(address.getCity())
            ||StringUtils.isBlank(address.getLine1())||StringUtils.isBlank(address.getPostcode())){
            throw FinhubExceptionUtil.exceptionFrom(ADDRESS_NOT_WHOLE_ERROR);
        }
        if ("AU".equals(address.getCountry().getCrCode()) || "US".equals(address.getCountry().getCrCode())){
            StateEnum stateEnum = StateEnum.getStateEnum(address.getState().getEnName());
            if (Objects.isNull(stateEnum)){
                throw FinhubExceptionUtil.exceptionFrom(ADDRESS_NOT_SUPPORT_ERROR);
            }
            address.getState().setShortEnName(stateEnum.getShortEnName());
        }
    }

    private void checkCompanyActiveAcct(String companyId) {
        //暂不需要
    }

    private void checkPhoneNumFormat(String phoneNum) {
        // TODO 手机号格式检查
    }

    private void checkEmailFormat(String email) {
        String emailRegex = "^[\\w-_\\.+]*[\\w-_\\.]\\@([\\w]+\\.)+[\\w]+[\\w]$";
        if (!email.matches(emailRegex)) {
            throw new FxCardException(EMAIL_FORMAT_ERROR);
        }

        // 检查邮箱域名是否为允许的个人邮箱域名
        if (!EmailCheckUtils.isAllowedPersonalEmailDomain(email)) {
            throw new FxCardException(EMAIL_DOMAIN_NOT_ALLOWED);
        }
    }

    private void phoneCheck(String phone, String applyId) {
        //手机号暂不判断（airwallex不要求手机号唯一）
        /*List<CardholderApplyListResDTO> list = this.queryByPhone(phone);
        if (StringUtils.isNotBlank(applyId)) {
            list = list.stream().filter(s -> !applyId.equals(s.getApplyId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(list)) {
            throw new FxCardException(PHONE_EXIST_EXCEPTION);
        }*/
    }

    private void emailCheck(String email, String applyId, String fxCardholderId) {
        checkEmailFormat(email);

        List<CardholderApplyListResDTO> list2 = this.queryByEmail(email);
        if (StringUtils.isNotBlank(applyId)) {
            list2 = list2.stream().filter(s -> !applyId.equals(s.getApplyId())).collect(Collectors.toList());
        }

        if (StringUtils.isNotBlank(fxCardholderId)) {
            list2 = list2.stream().filter(s -> !fxCardholderId.equals(s.getFxCardholderId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(list2)) {
            throw new FxCardException(EMAIL_EXIST_EXCEPTION);
        }
    }


    public CardholderApplyDTO findByApplyId(String applyId) {
        if(StringUtils.isBlank(applyId)){
            throw FinhubExceptionUtil.exceptionFrom(ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderApplyPO.DB_COL_APPLY_ID, applyId);
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, 0);
        CardholderApplyDTO applyDTO = this.findOne(queryWrapper);
        return applyDTO;
    }

    public List<CardholderDetailResDTO> convert2CardholderDetailResDTOS(List<CardholderApplyDTO> applyDTOS) {
        List<CardholderDetailResDTO> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(applyDTOS)){
            return list;
        }
        applyDTOS.forEach(p->{
            list.add(convert2CardholderDetailResDTO(p));
        });
        return list;
    }

    public CardholderDetailResDTO convert2CardholderDetailResDTO(CardholderApplyDTO applyDTO) {
        CardholderDetailResDTO resDTO = new CardholderDetailResDTO();
        BeanUtils.copyProperties(applyDTO, resDTO);
        resDTO.setApplyId(applyDTO.getApplyId());
        resDTO.setFxCardholderId(applyDTO.getFxCardholderId());
        resDTO.setName(applyDTO.getName());
        resDTO.setBirth(applyDTO.getBirth());
        resDTO.setNationCode(applyDTO.getNationCode());
        resDTO.setPhone(applyDTO.getPhone());
        resDTO.setAddressDto(JsonUtils.toObj(applyDTO.getAddress(), AddressDto.class));
        resDTO.setPostalAddressDto(JsonUtils.toObj(applyDTO.getPostalAddress(), AddressDto.class));
        resDTO.setIdentificationType(applyDTO.getIdentificationType());
        resDTO.setIdentificationNumber(applyDTO.getIdentificationNumber());
        resDTO.setIdentificationExpiryDate(applyDTO.getIdentificationExpiryDate());

        CardholderApplyShowStatusEnum showStatusEnum = CardholderApplyShowStatusEnum.getEnumWhithApplyStatus(applyDTO.getApplyStatus());
        if(Objects.nonNull(showStatusEnum)){
            resDTO.setShowStatus(showStatusEnum.getKey());
            resDTO.setShowStatusStr(showStatusEnum.getDesc());
        }
        CardPlatformEnum cardPlatformEnum  = CardPlatformEnum.getPlatform(applyDTO.getCardPlatform());
        resDTO.setCardPlatformName(cardPlatformEnum.getName());
        return resDTO;
    }

    public CardholderApplyDTO applyModify(CardholderApplyAddReqDTO reqDTO) {

        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(user.getUser_id(), user.getCompany_id());

        log.info("CardholderApplyManager applyModify reqDTO={},employeeContract={}", JsonUtils.toJson(reqDTO), JsonUtils.toJson(employeeContract));
        String lockKey = APPLY_MODIFY_REDIS_KEY_PRE + reqDTO.getApplyId();
        try {
            boolean tryLock = redissonService.tryLock(lockKey);
            if (!tryLock) {
                throw new FxCardException(OPERATION_OFTEN);
            }
            return doApplyModify(reqDTO, employeeContract);
        } catch (InterruptedException e) {
            throw new FxCardException(OPERATION_OFTEN);
        } catch (FinhubException e) {
            log.warn("【持卡人申请修改异常】：{}", e);
            throw e;
        } catch (Exception e) {
            log.error("【持卡人申请修改异常】：{}", e);
            throw new FxCardException(EXCEPTION);
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                log.error("持卡人申请修改释放锁失败：{}", lockKey, e);
            }
        }
    }

    private CardholderApplyDTO doApplyModify(CardholderApplyAddReqDTO reqDTO, EmployeeContract employee) {
        applyCheck(reqDTO, employee);
        if (StringUtils.isBlank(reqDTO.getApplyId())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        // 如果申请不再是待审核，则不修改
        CardholderApplyDTO byApplyId = this.findByApplyId(reqDTO.getApplyId());
        if (byApplyId == null || 1 != byApplyId.getApplyStatus()) {
            throw new FxCardException(APPLY_NOT_EXIST);
        }
        // 保持原姓名
        reqDTO.setFirstName(byApplyId.getFirstName());
        reqDTO.setLastName(byApplyId.getLastName());
        CardholderApplyPO cardholderApplyPO = new CardholderApplyPO();
        BeanUtils.copyProperties(reqDTO, cardholderApplyPO);;
        identificationHandle(cardholderApplyPO, reqDTO.getIdentificationExpiryType(), reqDTO.getIdentificationExpiryDate());
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardholderApplyPO.DB_COL_APPLY_ID, reqDTO.getApplyId());
        boolean update = this.update(cardholderApplyPO, updateWrapper);
        if(!update){
            throw new FxCardException(GlobalCoreResponseCode.COMMIT_CARDHOLDER_UPDATE_ERROR);
        }
        return converter.poToDTO(cardholderApplyPO);
    }

    public Boolean applyApprove(String applyId, Integer status, String reason) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        log.info("CardholderApplyManager applyApprove applyId={}, status={},user={}", applyId, status, JsonUtils.toJson(user));

        CardholderApplyDTO applyDTO = this.findByApplyId(applyId);
        approveCheck(status, user, applyDTO);

        String lockKey = APPLY_MODIFY_REDIS_KEY_PRE + applyId;
        try {
            boolean tryLock = redissonService.tryLock(lockKey);
            if (!tryLock) {
                throw new FxCardException(OPERATION_OFTEN);
            }

            //拒绝审批
            if(CardholderApplyStatusEnum.isRefuse(status)){
                return updateApproveRefuse(applyId, status, reason, user);
            }

            //审批通过，通过发起airwallex创建持卡人调用
            AirCreateCardholderRpcRspDTO cardholder = null;
            if (StringUtils.isBlank(applyDTO.getBankCardholderId())){
                AirCreateCardHolderRpcReqDTO rpcReqDTO = buildAirCreateCardHolderRpcReqDTO(applyDTO);
                cardholder = doAirCreateCardholder(rpcReqDTO, applyDTO.getApplyId(), user);
            } else {
                AirUpdateCardHolderRpcReqDTO rpcReqDTO = buildAirUpdateCardHolderRpcReqDTO(applyDTO);
                cardholder = doAirUpdateCardholder(rpcReqDTO, applyId, user);
            }

            if (Objects.isNull(cardholder)){
                log.warn("发起airwallex调用创建持卡人后没有返回持卡人信息");
                //创建失败
                return false;
            }

            //银行审核中,更新持卡人id和状态
            boolean b = updateBankDealingStatus(applyId, CardholderApplyStatusEnum.BANK_DEALING.getKey(), cardholder.getCardholder_id(), user);
            applyDTO.setBankCardholderId(cardholder.getCardholder_id());
            applyDTO.setApplyStatus(CardholderApplyStatusEnum.BANK_DEALING.getKey());
            if (b){
                //发起异步调用查询持卡人状态
                asynAirQueryResult(applyDTO);
            }
            return b;

        } catch (FinhubException e) {
            log.warn("【持卡人申请审批修改异常】：{}", e);
            throw e;
        } catch (Exception e) {
            log.error("【持卡人申请审批修改异常】：{}", e);
            throw new FxCardException(EXCEPTION);
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                log.error("持卡人申请审批释放锁失败：{}", lockKey, e);
            }
        }
    }

    private void approveCheck(Integer status, UserComInfoVO user, CardholderApplyDTO applyDTO) {
        if (Objects.isNull(applyDTO) || !CardholderApplyStatusEnum.canApproveStatus(applyDTO.getApplyStatus())) {
            throw new FxCardException(APPLY_NOT_EXIST);
        }
        //判断是否是审核状态
        if(!CardholderApplyStatusEnum.isApproveStatus(status)){
            throw new FxCardException(APPLY_APPROVE_STATUS_ERROR);
        }
        //判断是否是同一个公司
        if(!Objects.equals(user.getCompany_id(), applyDTO.getCompanyId())){
            throw new FxCardException(APPROVE_NO_AUTH);
        }
    }

    private void asynAirQueryResult(CardholderApplyDTO applyDTO) {
        CompletableFuture.runAsync(()->{
            try {
                Thread.sleep(2000);//等待2s后发起调用
                bankDealingApplyHandle(applyDTO);
            } catch (Exception e){
                log.error("异步查询持卡人创建结果报错，applyDTO={}", JsonUtils.toJson(applyDTO), e);
            }
        }, AirThreadPoolExecutor.airQueryExecutorInstance);
    }

    /**
     * 调用airwallex创建持卡人
     * @param rpcReqDTO
     * @return
     */
    private AirCreateCardholderRpcRspDTO doAirCreateCardholder(AirCreateCardHolderRpcReqDTO rpcReqDTO, String applyId, UserComInfoVO user) {
        try {
            log.info("doAirCreateCardholder params={}", JsonUtils.toJson(rpcReqDTO));
            AirCreateCardholderRpcRspDTO cardholder = iAirWallexCardHolderService.createCardholder(rpcReqDTO);
            log.info("doAirCreateCardholder res={}", JsonUtils.toJson(cardholder));
            return cardholder;
        } catch (FinhubException e){
            //TODO 银行已经存在的情况的处理，能取到CardholderId？
            log.warn("doAirCreateCardholder warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            //更新银行失败
            updateApplyFail(applyId, e.getMessage(), user);
            //throw e;
            return null;
        } catch (Exception e){
            log.error("doAirCreateCardholder error params={}", JsonUtils.toJson(rpcReqDTO), e);
            //更新银行失败
            updateApplyFail(applyId, e.getMessage(), user);
            //throw FinhubExceptionUtil.exceptionFrom(EXCEPTION);
            return null;
        }
    }

    private AirCreateCardHolderRpcReqDTO buildAirCreateCardHolderRpcReqDTO(CardholderApplyDTO applyDTO) {
        AirCreateCardHolderRpcReqDTO rpcReqDTO = new AirCreateCardHolderRpcReqDTO();
        rpcReqDTO.setEmail(applyDTO.getEmail());
        String mobileNumber = StringUtils.isNotBlank(applyDTO.getNationCode()) ? applyDTO.getNationCode() : NATION_CODE_CHN ;
        rpcReqDTO.setMobile_number(mobileNumber + "-" + applyDTO.getPhone());

        //持卡人地址
        String address1 = applyDTO.getAddress();
        BaseAirwallexRpcDTO.Address address = buildAddress(address1);
        rpcReqDTO.setAddress(address);

        //卡片邮寄地址（设置固定邮寄地址） TODO
        /*BaseAirwallexRpcDTO.Address postAddress = JSONObject.parseObject(applyDTO.getPostalAddress(), BaseAirwallexRpcDTO.Address.class);
        rpcReqDTO.setPostalAddress(postAddress);*/

        //个人信息
        BaseAirwallexRpcDTO.Individual individual = new BaseAirwallexRpcDTO.Individual();
        individual.setExpress_consent_obtained("yes");
        individual.setDate_of_birth(DateUtils.formatDate(applyDTO.getBirth()));
        //名字
        BaseAirwallexRpcDTO.Name name = new BaseAirwallexRpcDTO.Name();
        name.setFirst_name(applyDTO.getFirstName());
        name.setLast_name(applyDTO.getLastName());
        individual.setName(name);

        //证件信息
        /*BaseAirwallexRpcDTO.Identification identification = new BaseAirwallexRpcDTO.Identification();
        identification.setCountry(applyDTO.getIdentificationCountry());
        identification.setExpiry_date(DateUtils.formatDate(applyDTO.getIdentificationExpiryDate()));
        identification.setNumber(applyDTO.getIdentificationNumber());
        String airType = IdentificationTypeEnum.getEnum(applyDTO.getIdentificationType()).getAirCode();
        identification.setType(airType);
        individual.setIdentification(identification);*/

        rpcReqDTO.setIndividual(individual);
        return rpcReqDTO;
    }

    private BaseAirwallexRpcDTO.Address buildAddress(String address1) {
        AddressDto addressDto = JSONObject.parseObject(address1, AddressDto.class);
        BaseAirwallexRpcDTO.Address address = new BaseAirwallexRpcDTO.Address();
        address.setCountry(addressDto.getCountry().getCrCode());
        countryExclude(addressDto, address);
        address.setState(addressDto.getState().getShortEnName());
        address.setCity(addressDto.getCity().getEnName());
        address.setLine1(addressDto.getLine1());
        address.setPostcode(addressDto.getPostcode());
        return address;
    }

    private void countryExclude(AddressDto addressDto, BaseAirwallexRpcDTO.Address address) {
        if("Hong Kong".equals(addressDto.getState().getEnName())){
            address.setCountry("HK");
        }
    }

    private boolean updateApproveRefuse(String applyId, Integer status, String reason, UserComInfoVO user) {
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set(CardholderApplyPO.DB_COL_APPLY_STATUS, status)
            .set(CardholderApplyPO.DB_COL_REFUSE_REASON, reason)
            .set(CardholderApplyPO.DB_COL_APPROVE_USER_ID, user.getUser_id())
            .set(CardholderApplyPO.DB_COL_APPROVE_USER_NAME, user.getUser_name())
            .set(CardholderApplyPO.DB_COL_APPROVE_TIME, new Date())
            .eq(CardholderApplyPO.DB_COL_APPLY_ID, applyId);
        return this.update(updateWrapper);
    }

    private boolean updateBankDealingStatus(String applyId, Integer status, String bankCardholderId, UserComInfoVO user) {
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set(CardholderApplyPO.DB_COL_APPLY_STATUS, status)
            .set(CardholderApplyPO.DB_COL_BANK_CARDHOLDER_ID, bankCardholderId)
            .set(CardholderApplyPO.DB_COL_APPROVE_USER_ID, user.getUser_id())
            .set(CardholderApplyPO.DB_COL_APPROVE_USER_NAME, user.getUser_name())
            .set(CardholderApplyPO.DB_COL_APPROVE_TIME, new Date())
            .set(CardholderApplyPO.DB_COL_REFUSE_REASON, "")
            .eq(CardholderApplyPO.DB_COL_APPLY_ID, applyId);
        return this.update(updateWrapper);
    }

    public Page<CardholderByPageResDTO> findCardholderApplyPageByStatus(CardholderByPageReqDTO reqDTO, CardholderApplyShowStatusEnum statusEnum) {
        Page<CardholderByPageResDTO> resPage;

        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getPhone()), CardholderPO.DB_COL_PHONE, reqDTO.getPhone());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getName()), CardholderPO.DB_COL_NAME, reqDTO.getName());
        queryWrapper.eq(CardholderApplyPO.DB_COL_COMPANY_ID, reqDTO.getCompanyId());
        queryWrapper.in(statusEnum != null, CardholderApplyPO.DB_COL_APPLY_STATUS, statusEnum.getStatus());
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, 0);
        queryWrapper.orderByDesc(CardholderApplyPO.DB_COL_CREATE_TIME);

        Page<CardholderApplyDTO> page = this.findPage(queryWrapper, reqDTO.getOffset(), reqDTO.getPageSize());
        List<CardholderByPageResDTO> cardholderByPageResDTOS = convert2CardholderByPageResDTO(page.getRecords());
        if (page.getTotal() == 0) {
            return new Page<>();
        }
        resPage = new Page(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setRecords(cardholderByPageResDTOS);
        return resPage;
    }

    public List<CardholderApplyDTO> findCardholderApplyByEmployeeId(CardholderOrApplyListReqDTO reqDto) {
        if (StringUtils.isBlank(reqDto.getEmployeeId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDto.getPhone()), CardholderPO.DB_COL_PHONE, reqDto.getPhone());
        queryWrapper.eq(StringUtils.isNotBlank(reqDto.getCardPlatform()),CardholderPO.DB_COL_CARD_PLATFORM, reqDto.getCardPlatform());
        queryWrapper.eq(CardholderApplyPO.DB_COL_EMPLOYEE_ID, reqDto.getEmployeeId());
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, 0);
        queryWrapper.in(CardholderApplyPO.DB_COL_APPLY_STATUS, Arrays.asList(CardholderApplyStatusEnum.APPLYING.getKey(), CardholderApplyStatusEnum.BANK_DEALING.getKey()));
        List<CardholderApplyDTO> list = this.findList(queryWrapper);
        return list;
    }

    private List<CardholderByPageResDTO> convert2CardholderByPageResDTO(List<CardholderApplyDTO> list) {
        List<CardholderByPageResDTO> resList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(list)){
            return resList;
        }
        for (CardholderApplyDTO applyDTO : list) {
            CardholderByPageResDTO resDTO = new CardholderByPageResDTO();
            resDTO.setApplyId(applyDTO.getApplyId());
            resDTO.setFxCardholderId(applyDTO.getFxCardholderId());
            resDTO.setName(applyDTO.getName());
            resDTO.setPhone(applyDTO.getPhone());
            resDTO.setCreateTime(applyDTO.getCreateTime());
            resDTO.setStatus(applyDTO.getApplyStatus());
            CardholderApplyStatusEnum enumByStatus = CardholderApplyStatusEnum.getEnum(applyDTO.getApplyStatus());
            if (null != enumByStatus) {
                resDTO.setStatusStr(enumByStatus.getMsg());
            }

            CardholderApplyShowStatusEnum showStatusEnum = CardholderApplyShowStatusEnum.getEnumWhithApplyStatus(applyDTO.getApplyStatus());
            if(Objects.nonNull(showStatusEnum)){
                resDTO.setShowStatus(showStatusEnum.getKey());
                resDTO.setShowStatusStr(showStatusEnum.getDesc());
            }

            resDTO.setRefuseReason(applyDTO.getRefuseReason());
            resList.add(resDTO);
        }
        return resList;
    }

    public CardholderDetailResDTO modify(CardholderModifyReqDTO reqDTO) {
        String lockKey = APPLY_MODIFY_REDIS_KEY_PRE + reqDTO.getApplyId();
        try {
            boolean tryLock = redissonService.tryLock(lockKey);
            if (!tryLock) {
                throw new FxCardException(OPERATION_OFTEN);
            }
            return doModify(reqDTO);
        } catch (InterruptedException e) {
            throw new FxCardException(OPERATION_OFTEN);
        } catch (FxCardException e) {
            log.warn("【持卡人申请修改并审批修改异常】：{}", e);
            throw e;
        } catch (Exception e) {
            log.error("【持卡人申请修改并审批修改异常】：{}", e);
            throw new FxCardException(EXCEPTION);
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                log.error("持卡人申请修改并审批释放锁失败：{}", lockKey, e);
            }
        }
    }

    private CardholderDetailResDTO doModify(CardholderModifyReqDTO reqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        // 企业账号是否正常
        checkCompanyActiveAcct(user.getCompany_id());
        CardholderApplyDTO applyDTO = this.findByApplyId(reqDTO.getApplyId());
        if (applyDTO == null || !CardholderApplyStatusEnum.canApproveStatus(applyDTO.getApplyStatus())) {
            throw new FxCardException(APPLY_NOT_EXIST);
        }

        // 检查手机号和邮箱是否已经在申请表中使用过了
        emailCheck(reqDTO.getEmail(), reqDTO.getApplyId(), applyDTO.getFxCardholderId());
        //地址信息检查
        addressCheck(reqDTO.getAddressDto());
        if (Objects.nonNull(reqDTO.getPostalAddressDto())){
            addressCheck(reqDTO.getPostalAddressDto());
        }

        CardholderApplyPO cardholderApplyPO = CopyUtils.convert(reqDTO, CardholderApplyPO.class);
        identificationHandle(cardholderApplyPO, reqDTO.getIdentificationExpiryType(), reqDTO.getIdentificationExpiryDate());
        cardholderApplyPO.setName(reqDTO.getName());
        cardholderApplyPO.setAddress(JsonUtils.toJson(reqDTO.getAddressDto()));
        cardholderApplyPO.setPostalAddress(JsonUtils.toJson(reqDTO.getPostalAddressDto()));
        cardholderApplyPO.setApplyStatus(CardholderApplyStatusEnum.APPLYING.getKey());
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardholderApplyPO.DB_COL_APPLY_ID, reqDTO.getApplyId());
        boolean update = this.update(cardholderApplyPO, updateWrapper);
        if(!update){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_UPDATE_ERROR);
        }

        CardholderApplyDTO applyDTONew = this.findByApplyId(reqDTO.getApplyId());

        return convert2CardholderDetailResDTO(applyDTONew);
    }

    private void identificationHandle(CardholderApplyPO cardholderApplyPO, Integer identificationExpiryType, String identificationExpiryDate) {
        if (IdentificationExpiryTypeEnum.isLongTime(identificationExpiryType)) {
            cardholderApplyPO.setIdentificationExpiryDate(null);
        } else if (IdentificationExpiryTypeEnum.isScopeTime(identificationExpiryType)) {
            if (StringUtils.isBlank(identificationExpiryDate)) {
                throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_EXPIRY_DATE_ERROR);
            }
            Date date = DateUtils.parseDate(identificationExpiryDate);
            if (Objects.isNull(date)) {
                throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_EXPIRY_DATE_FORMAT_ERROR);
            }
            cardholderApplyPO.setIdentificationExpiryDate(date);
        } else {
            throw FinhubExceptionUtil.exceptionFrom(ILLEGAL_ARGUMENT);
        }
    }

    public CardholderDetailResDTO createModify(CardholderModifyReqDTO reqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        log.info("CardholderApplyManager createModify companyId={},userId={},reqDTO={}", user.getCompany_id(), user.getUser_id(), JsonUtils.toJson(reqDTO));
        //持卡人信息检查
        CardholderDTO cardholderDTO = CardholderManager.me().findByCardholderId(reqDTO.getFxCardholderId());

        if (Objects.isNull(cardholderDTO)){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDER_NOT_EXIST);
        }
        //持卡人状态检查
        if (!CardholderStatusEnum.isEffective(cardholderDTO.getHolderStatus())){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDER_STATUS_NOT_SUPPORT_UPDATE);
        }

        // 检查手机号和邮箱是否已经在申请表中使用过了
        emailCheck(reqDTO.getEmail(), reqDTO.getApplyId(), cardholderDTO.getFxCardholderId());
        //地址信息检查
        addressCheck(reqDTO.getAddressDto());
        if (Objects.nonNull(reqDTO.getPostalAddressDto())){
            addressCheck(reqDTO.getPostalAddressDto());
        }

        //查询是否有在途的更新申请
        CardholderOrApplyListReqDTO reqDto = new CardholderOrApplyListReqDTO();
        reqDto.setEmployeeId(cardholderDTO.getEmployeeId());
        List<CardholderApplyDTO> cardholderApplyDTOS = this.findCardholderApplyByEmployeeId(reqDto);
        if (CollectionUtils.isNotEmpty(cardholderApplyDTOS)){
            throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_UPDATE_HAS_EXIST);
            /*f (cardholderApplyDTOS.size() > 1){
                throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_ERROR);
            }
            Integer applyStatus = cardholderApplyDTOS.get(0).getApplyStatus();
            if (CardholderApplyStatusEnum.isBankDealing(applyStatus)){
                throw FinhubExceptionUtil.exceptionFrom(CARDHOLDERAPPLY_IS_BANK_DEALING);
            }
            //更新申请单信息
            if (CardholderApplyStatusEnum.isApplying(applyStatus)){
                reqDTO.setApplyId(cardholderApplyDTOS.get(0).getApplyId());
                this.doModify(reqDTO);
            }*/
        }
        //创建更新申请
        CardholderApplyDTO applyDTO = doCreateModify(reqDTO, user, cardholderDTO);

        return convert2CardholderDetailResDTO(applyDTO);
    }

    private CardholderApplyDTO doCreateModify(CardholderModifyReqDTO reqDTO, UserComInfoVO user, CardholderDTO cardholderDTO) {
        // 企业账号是否正常
        checkCompanyActiveAcct(user.getCompany_id());

        CardholderApplyPO cardholderApplyPO = CopyUtils.convert(reqDTO, CardholderApplyPO.class);

        identificationHandle(cardholderApplyPO, reqDTO.getIdentificationExpiryType(), reqDTO.getIdentificationExpiryDate());
        cardholderApplyPO.setBankCardholderId(cardholderDTO.getBankCardholderId());
        cardholderApplyPO.setCompanyId(cardholderDTO.getCompanyId());
        cardholderApplyPO.setCardPlatform(cardholderDTO.getCardPlatform());
        cardholderApplyPO.setEmployeeId(cardholderDTO.getEmployeeId());
        cardholderApplyPO.setEmployeeName(cardholderDTO.getEmployeeName());
        cardholderApplyPO.setCreateUserId(user.getUser_id());
        // 申请id
        cardholderApplyPO.setApplyId(BizIdUtils.getCardholderApplyId());
        // 邮寄地址
        cardholderApplyPO.setAddress(JsonUtils.toJson(reqDTO.getAddressDto()));
        cardholderApplyPO.setPostalAddress(JsonUtils.toJson(reqDTO.getPostalAddressDto()));
        // 状态是更新
        cardholderApplyPO.setApplyType(ApplyTypeEnum.UPDATE.getCode());
        // 审批成功
        cardholderApplyPO.setApplyStatus(CardholderApplyStatusEnum.APPLYING.getKey());

        this.save(cardholderApplyPO);

        return this.converter.poToDTO(cardholderApplyPO);
    }

    private AirUpdateCardHolderRpcReqDTO buildAirUpdateCardHolderRpcReqDTO(CardholderApplyDTO applyDTO) {
        AirUpdateCardHolderRpcReqDTO rpcReqDTO = new AirUpdateCardHolderRpcReqDTO();
        rpcReqDTO.setEmail(applyDTO.getEmail());
        String mobileNumber = StringUtils.isNotBlank(applyDTO.getNationCode()) ? applyDTO.getNationCode() : NATION_CODE_CHN ;
        rpcReqDTO.setMobile_number(mobileNumber + "-" + applyDTO.getPhone());
        rpcReqDTO.setCardholder_id(applyDTO.getBankCardholderId());
        //持卡人地址
        String address1 = applyDTO.getAddress();
        BaseAirwallexRpcDTO.Address address = buildAddress(address1);
        rpcReqDTO.setAddress(address);

        //卡片邮寄地址（设置固定邮寄地址）
        /*BaseAirwallexRpcDTO.Address postAddress = JSONObject.parseObject(applyDTO.getPostalAddress(), BaseAirwallexRpcDTO.Address.class);
        rpcReqDTO.setPostalAddress(postAddress);*/

        //个人信息
        BaseAirwallexRpcDTO.Individual individual = new BaseAirwallexRpcDTO.Individual();
        individual.setExpress_consent_obtained("yes");
        individual.setDate_of_birth(DateUtils.formatDate(applyDTO.getBirth()));
        //名字
        BaseAirwallexRpcDTO.Name name = new BaseAirwallexRpcDTO.Name();
        name.setFirst_name(applyDTO.getFirstName());
        name.setLast_name(applyDTO.getLastName());
        individual.setName(name);

        //证件信息
        /*BaseAirwallexRpcDTO.Identification identification = new BaseAirwallexRpcDTO.Identification();
        identification.setCountry(applyDTO.getIdentificationCountry());
        identification.setExpiry_date(DateUtils.formatDate(applyDTO.getIdentificationExpiryDate()));
        identification.setNumber(applyDTO.getIdentificationNumber());
        String airType = IdentificationTypeEnum.getEnum(applyDTO.getIdentificationType()).getAirCode();
        identification.setType(airType);
        individual.setIdentification(identification);*/

        rpcReqDTO.setIndividual(individual);
        return rpcReqDTO;
    }

    /**
     * 调用airwallex更新持卡人
     * @param rpcReqDTO
     * @return
     */
    private AirCreateCardholderRpcRspDTO doAirUpdateCardholder(AirUpdateCardHolderRpcReqDTO rpcReqDTO, String applyId, UserComInfoVO user) {
        try {
            log.info("doAirUpdateCardholder params={}", JsonUtils.toJson(rpcReqDTO));
            AirCreateCardholderRpcRspDTO rpcRspDTO = iAirWallexCardHolderService.updateCardholder(rpcReqDTO);
            log.info("doAirUpdateCardholder res={}", JsonUtils.toJson(rpcRspDTO));
            return rpcRspDTO;
        } catch (FinhubException e){
            log.warn("doAirUpdateCardholder warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            updateApplyFail(applyId, e.getMessage(), user);
            return null;
        } catch (Exception e){
            log.error("doAirUpdateCardholder error params={}", JsonUtils.toJson(rpcReqDTO), e);
            updateApplyFail(applyId, e.getMessage(), user);
            return null;
        }
    }

    private void updateApplyFail(String applyId, String message, UserComInfoVO user) {
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardholderApplyPO.DB_COL_APPLY_ID, applyId);
        updateWrapper.set(CardholderApplyPO.DB_COL_APPLY_STATUS, CardholderApplyStatusEnum.BANK_REFUSE.getKey());
        updateWrapper.set(CardholderApplyPO.DB_COL_REFUSE_REASON, message);
        updateWrapper.set(CardholderApplyPO.DB_COL_APPROVE_USER_ID, user.getUser_id());
        updateWrapper.set(CardholderApplyPO.DB_COL_APPROVE_USER_NAME, user.getUser_name());
        updateWrapper.set(CardholderApplyPO.DB_COL_APPROVE_TIME, new Date());
        this.update(updateWrapper);
    }


    /**
     * 获取银行处理中的大于1分钟的数据
     * 卡id不等于空的
     * @return
     */
    public List<CardholderApplyDTO> queryBankDealingApplys() {
        QueryWrapper<CardholderApplyPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        queryWrapper.eq(CardholderApplyPO.DB_COL_APPLY_STATUS, CardholderApplyStatusEnum.BANK_DEALING.getKey());
        queryWrapper.le(CardholderApplyPO.DB_COL_UPDATE_TIME, DateUtils.addMinute(new Date(), -1));
        queryWrapper.isNotNull(CardholderApplyPO.DB_COL_BANK_CARDHOLDER_ID);
        queryWrapper.orderByAsc(CardholderApplyPO.DB_COL_ID);
        queryWrapper.last("limit 500");
        return this.findList(queryWrapper);
    }

    /**
     * 审核中的持卡人状态轮询
     */
    public void handleBankDealingData(){
        //获取银行处理中的数据
        List<CardholderApplyDTO> applyListResDTOS = this.queryBankDealingApplys();
        if (CollectionUtils.isEmpty(applyListResDTOS)){
            return;
        }

        //循环处理
        applyListResDTOS.forEach(p -> {
            bankDealingApplyHandle(p);
        });

    }

    private void bankDealingApplyHandle(CardholderApplyDTO applyDto) {
        log.info("bankDealingApplyHandle fxCardholderId={},bankCardholderId={}", applyDto.getFxCardholderId(), applyDto.getBankCardholderId());
        //如果没有银行的卡id，需要获取通道的全部卡接口，获取回来后看里面有没有我们的卡，如果没有则更改创建失败（暂不可行，只能通过全量对账处理）
        if (StringUtils.isBlank(applyDto.getBankCardholderId())){
            return;
        }

        try {
            //如果有银行的卡ID，直接调用通道的卡详情接口，如果创建完成，更改创建结果
            CardHolderRpcRespDTO cardHolderDetails = iAirWallexCardHolderService.getCardHolderDetails(applyDto.getBankCardholderId());
            if (Objects.isNull(cardHolderDetails)){
                log.warn("银行持卡人信息获取为空，bankCardHolderId={}", applyDto.getBankCardholderId());
                return;
            }

            String status = cardHolderDetails.getStatus();
            String bankCardholderId = applyDto.getBankCardholderId();

            CardholderAirwallexStatusEnum anEnum = CardholderAirwallexStatusEnum.getEnum(status);
            if (Objects.isNull(anEnum)){
                log.error("申请--当前持卡人状态不能被识别，cardholderId={},status={}", bankCardholderId, status);
                return;
            }

            if (!CardholderAirwallexStatusEnum.isReady(status)){
                log.info("申请--持卡人处于不可处理状态，cardholderId={},status={}", bankCardholderId, status);
                return;
            }

            //更新申请单状态银行成功
            CardholderApplyPO applyPO = buildCardholderApplyPO(applyDto, cardHolderDetails);
            this.updateById(applyPO);
            //持卡人更新或者新建
            CardholderDTO cardholderDTO = CardholderManager.me().findByBankCardholderId(bankCardholderId);
            CardholderApplyDTO cardholderApplyDTO = this.findByApplyId(applyDto.getApplyId());
            if (Objects.isNull(cardholderDTO)){
                //新建持卡人
                CardholderPO addReqDTO = CopyUtils.convert(cardholderApplyDTO, CardholderPO.class);
                addReqDTO.setHolderStatus(CardholderStatusEnum.EFFECTIVE.getKey());
                CardholderManager.me().save(addReqDTO);
            } else {
                //更新持卡人
                CardholderPO cardholderPO = new CardholderPO();
                BeanUtils.copyProperties(cardholderApplyDTO, cardholderPO, CopyUtils.getNullPropertyNames(cardholderApplyDTO));
                cardholderPO.setId(cardholderDTO.getId());
                cardholderPO.setHolderStatus(CardholderStatusEnum.EFFECTIVE.getKey());
                CardholderManager.me().updateById(cardholderPO);
            }

        } catch (Exception e){
            log.warn("持卡人银行审批中状态处理异常，bankCardHolderId={}", applyDto.getBankCardholderId(), e);
        }
    }


    private CardholderApplyPO buildCardholderApplyPO(CardholderApplyDTO applyDto, CardHolderRpcRespDTO cardHolderDetails){
        CardholderApplyPO applyPO = new CardholderApplyPO();
        applyPO.setId(applyDto.getId());
        applyPO.setApplyStatus(CardholderApplyStatusEnum.BANK_PASS.getKey());
        return applyPO;
    }


    /**
     * 删除
     * @return
     */
    public Boolean removeById(String id) {
        UpdateWrapper<CardholderApplyPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardholderApplyPO.DB_COL_ID, id);
        updateWrapper.set(CardholderApplyPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.DEL.getCode());
        return this.update(updateWrapper);
    }
}
